
                            The SwiftPM Project
                            ====================

Please visit the SwiftPM web site for more information:

  * https://github.com/swiftlang/swift-package-manager

Copyright (c) 2014 - 2021 Apple Inc. and the Swift project authors

The Swift Project licenses this file to you under the Apache License,
version 2.0 (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at:

  https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.

Also, please refer to each LICENSE.txt file, which is located in
the 'license' directory of the distribution file, for the license terms of the
components that this product depends on.

-------------------------------------------------------------------------------

The dependency resolver is influenced by Dart project (pub tool).

  * LICENSE (BSD 3-Clause "New" or "Revised" License):
    * https://github.com/dart-lang/pub/blob/master/LICENSE
  * HOMEPAGE:
    * https://dart.dev
    * https://github.com/dart-lang/pub

---

This product contains a derivation of Vapor's JWTKit library, specifically `JWTParser.swift`, `JWTSerializer.swift` and `Base64URL.swift`.

  * LICENSE (MIT License):
    * https://www.apache.org/licenses/LICENSE-2.0
  * HOMEPAGE:
    * https://vapor.codes
    * https://github.com/vapor/jwt-kit

---

This product contains a derivation of OpenSSL's OCSP implementation, found under the `PackageCollectionsSigningLibc` module.

  * LICENSE (Apache License 2.0):
    * https://www.apache.org/licenses/LICENSE-2.0
  * HOMEPAGE:
    * https://github.com/openssl/openssl

---

The observability system is influenced by SwiftLog project.

  * LICENSE (Apache License 2.0):
    * https://www.apache.org/licenses/LICENSE-2.0
  * HOMEPAGE:
    * https://github.com/apple/swift-log

---

The observability system is influenced by SwiftMetrics project.

  * LICENSE (Apache License 2.0):
    * https://www.apache.org/licenses/LICENSE-2.0
  * HOMEPAGE:
    * https://github.com/apple/swift-metrics

---

The observability system is influenced by SwiftDistributedTracing project.

  * LICENSE (Apache License 2.0):
    * https://www.apache.org/licenses/LICENSE-2.0
  * HOMEPAGE:
    * https://github.com/apple/swift-distributed-tracing-baggage

---

# ``scipio``

A new build tool to generate XCFramework from Swift Package

@Metadata {
    @DisplayName("Scipio")
    @DocumentationExtension(mergeBehavior: override)
}

## Overview


## Topics

### Basic Usage

- <doc:installation>
- <doc:prepare-cache-for-applications>
- <doc:create-frameworks>

### Advanced Usage

- <doc:cache-system>
- <doc:build-pipeline>
- <doc:using-s3-storage>
- <doc:mergeable-library>

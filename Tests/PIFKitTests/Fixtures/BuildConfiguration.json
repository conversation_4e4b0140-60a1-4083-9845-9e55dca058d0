{"buildSettings": {"CLANG_ENABLE_OBJC_ARC": "YES", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_IDENTITY": "", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "DRIVERKIT_DEPLOYMENT_TARGET": "19.0", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_NS_ASSERTIONS": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_TESTING_SEARCH_PATHS": "YES", "ENTITLEMENTS_REQUIRED": "NO", "FRAMEWORK_SEARCH_PATHS[__platform_filter=ios;ios-simulator]": ["$(inherited)", "$(PLATFORM_DIR)/Developer/Library/Frameworks"], "FRAMEWORK_SEARCH_PATHS[__platform_filter=macos]": ["$(inherited)", "$(PLATFORM_DIR)/Developer/Library/Frameworks"], "FRAMEWORK_SEARCH_PATHS[__platform_filter=tvos;tvos-simulator]": ["$(inherited)", "$(PLATFORM_DIR)/Developer/Library/Frameworks"], "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": ["$(inherited)", "SWIFT_PACKAGE", "DEBUG=1"], "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "IPHONEOS_DEPLOYMENT_TARGET[__platform_filter=ios-maccatalyst]": "13.0", "KEEP_PRIVATE_EXTERNS": "NO", "MACOSX_DEPLOYMENT_TARGET": "10.13", "ONLY_ACTIVE_ARCH": "YES", "OTHER_LDRFLAGS": [], "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SKIP_INSTALL": "YES", "SUPPORTED_PLATFORMS": ["$(AVAILABLE_PLATFORMS)"], "SWIFT_ACTIVE_COMPILATION_CONDITIONS": ["$(inherited)", "SWIFT_PACKAGE", "DEBUG"], "SWIFT_INSTALL_OBJC_HEADER": "NO", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "TVOS_DEPLOYMENT_TARGET": "12.0", "USE_HEADERMAP": "NO", "WATCHOS_DEPLOYMENT_TARGET": "4.0", "XROS_DEPLOYMENT_TARGET": "1.0"}, "guid": "PACKAGE:/Users/<USER>/work/Swift/build-playground/MyFramework::BUILDCONFIG_Debug", "impartedBuildProperties": {"buildSettings": {}}, "name": "Debug"}
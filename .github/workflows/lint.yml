name: Lint

on: 
  push:
    branches:
      - main
  pull_request:
    branches:
      - '*'
jobs:
  SwiftLint:
    strategy:
      matrix:
        xcode_version: ["16.4"]
    env: 
      DEVELOPER_DIR: "/Applications/Xcode_${{ matrix.xcode_version }}.app/Contents/Developer"
    runs-on: macos-15
    steps:
    - uses: actions/checkout@v4
    - name: Install SwiftLint
      run: brew install swiftlint
    - name: SwiftLint
      run: swiftlint --strict

on:
  release:
    types: [published, edited]
  workflow_dispatch:
name: Upload Artifact Bundle to Release
env:
  DEVELOPER_DIR: '/Applications/Xcode_16.4.app/Contents/Developer'
jobs:
  release:
    runs-on: macos-15
    steps:
      - uses: actions/checkout@v4
      # Scipio can't be built with Xcode build mode, so it can't be a universal binary.
      - name: Build for arm64
        run: swift build --disable-sandbox -c release --arch arm64 
      - name: Build for x86_64
        run: swift build --disable-sandbox -c release --arch x86_64
      - uses: giginet/github-action-artifactbundle@v2
        id: artifactbundle
        with:
          artifact_name: scipio
      - name: Upload Artifact Bundle to Release
        run: |
          BODY="${{ github.event.release.body }}"
          BUNDLE_PATH="${{ steps.artifactbundle.outputs.bundle_path }}"
          SHA256="${{ steps.artifactbundle.outputs.bundle_sha256 }}"
          TAG_NAME="${{ github.event.release.tag_name }}"
          gh release upload "${TAG_NAME}" "${BUNDLE_PATH}"
          NEW_BODY="$(printf "%s\n%s" "$BODY" "Artifact bundle checksum: ``$SHA256``")"
          gh release edit "${TAG_NAME}" --notes "${NEW_BODY}"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

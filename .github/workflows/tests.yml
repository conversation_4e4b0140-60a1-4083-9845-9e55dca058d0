name: Tests

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - '*'
jobs:
  Tests:
    timeout-minutes: 15
    strategy:
      matrix:
        xcode_version:
          - "16.4" # 6.1.2
    env:
      DEVELOPER_DIR: "/Applications/Xcode_${{ matrix.xcode_version }}.app/Contents/Developer"
    runs-on: macos-15
    steps:
    - name: Get swift version
      run: swift --version
    - uses: actions/checkout@v4
    - name: Build
      run: |
        swift build
    - name: Run Tests
      run: |
        swift test --no-parallel
      env:
        ENABLE_INTEGRATION_TESTS: 1
        IS_CI: 1
